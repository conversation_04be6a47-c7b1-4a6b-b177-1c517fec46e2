import React, { useState, useEffect, useRef } from 'react';
import {
  Container,
  Paper,
  Typography,
  Box,
  TextField,
  Button,
  IconButton,
  CircularProgress,
  Alert
} from '@mui/material';
import {
  Send as SendIcon,
  ArrowBack as ArrowBackIcon,
  Psychology as AIIcon,
  Folder as WorkspaceIcon,
  ExpandLess as ExpandLessIcon,
  ExpandMore as ExpandMoreIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { useNavigate, useLocation, useParams } from 'react-router-dom';
import DashboardHeader from '../../components/DashboardHeader';
import MonacoWorkspace from '../../components/MonacoWorkspace';
import LoadingPage from '../../components/LoadingPage';
import { interviewAPI, buildAPI, chatAPI, ecsWorkspaceAPI, llmAPI, ChatMessage } from '../../utils/api';
import { ProjectData, UserData, transformNewSchemaToFlatAnswers } from '../../utils/dataTransform';

import BuildChatbox from '../../components/BuildChatbox';
import HistoryDrawer, { ConversationItem } from '../../components/HistoryDrawer';
import { useAuth } from '../../context/AuthContext';
import { motion, AnimatePresence } from 'framer-motion';
import drawerOpener from '../../assets/images/drawerOpener.png';
import AccountModal from '@/components/account/AccountModal';

interface LocationState {
  prompt?: string;
  projectData?: ProjectData;
  projectDataOld?: any;
  functionalAnswers?: { [key: string]: string };
  technicalAnswers?: { [key: string]: string };
  user?: UserData;
  shouldStartBuild?: boolean; // Flag to indicate fresh build should be started immediately
  llmResponse?: {
    success: boolean;
    content: string;
    buildResultUuid?: string;
    extractedContent?: {
      description?: string;
      codeBlocks: any[];
      projectStructure?: any;
      deploymentInstructions?: string;
      additionalSections?: { [key: string]: string };
    };
    metadata: any;
  };
  buildResponse?: {
    success: boolean;
    message: string;
    buildResult?: {
      uuid: string;
      description?: string;
      deploymentInstructions?: string;
      additionalSections?: { [key: string]: string };
      status: string;
      codeBlocks: any[];
      projectStructure?: any;
    };
    workspace?: {
      workspaceId: string;
      workspaceUrl: string;
      status: string;
    };
    metadata: any;
  };
  llmError?: string;
}

interface InterviewData {
  uuid: string;
  user: UserData;
  projectData: ProjectData;
}

// ChatMessage interface is now imported from utils/api

const Build: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { uuid } = useParams<{ uuid?: string }>();
  const { user } = useAuth();
  const [openHistoryView, setOpenHistoryView] = useState(false);
  const [allConversations, setAllConversations] = useState<ConversationItem[]>([]);
  const [activeConversation, setActiveConversation] = useState<string>(uuid || '');
  const [prompt, setPrompt] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [interviewData, setInterviewData] = useState<InterviewData | null>(null);
  const [isLoading, setIsLoading] = useState(!!uuid);
  const [error, setError] = useState<string | null>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [projectStructure, setProjectStructure] = useState<any>(null);
  const [isGeneratingCode, setIsGeneratingCode] = useState(false);
  const [hasCodeGenerated, setHasCodeGenerated] = useState(false);
  const [buildProgress, setBuildProgress] = useState({
    llmGenerating: false,
    llmCompleted: false,
    workspaceCreating: false,
    workspaceCompleted: false,
    filesUploading: false,
    completed: false
  });
  const [isPreviewLoading, setIsPreviewLoading] = useState(false);
  const [currentWorkspaceId, setCurrentWorkspaceId] = useState<string | null>(null);
  const [existingBuildResult, setExistingBuildResult] = useState<any>(null);
  const [currentBuildResultUuid, setCurrentBuildResultUuid] = useState<string | null>(null);
  const [isWorkspaceReady, setIsWorkspaceReady] = useState(false);
  const [isPublishing, setIsPublishing] = useState(false);
  const [liveUrl, setLiveUrl] = useState<string | null>(null);
  const [showAccountModal, setShowAccountModal] = useState(false);
  const [isChatboxMinimized, setIsChatboxMinimized] = useState(false);
  const hasAutoMinimizedRef = useRef<boolean>(false); // Add ref to track auto-minimize

  useEffect(() => {
    setActiveConversation(uuid || '');
  }, [uuid]);

  useEffect(() => {
    const fetchHistory = async () => {
      if (!user?.id) return;
      try {
        const res = await interviewAPI.getUserHistory(user.id, { limit: 50 });
        if (res.success) {
          const mapped: ConversationItem[] = res.data.map(h => ({
            _id: h.uuid,
            title: h.name || h.prompt,
            createdAt: h.createdAt,
            updatedAt: h.updatedAt
          }));
          setAllConversations(mapped);
        }
      } catch (e) {
        console.error('Failed to fetch history', e);
      }
    };
    fetchHistory();
  }, [user?.id]);

  const handleSetActiveConversation = (id: string) => {
    setActiveConversation(id);
    navigate(`/build/${id}`);
    setOpenHistoryView(false);
  };

  const handleDeleteConversation = async (id: string) => {
    try {
      const isActive = activeConversation === id;
      if (isActive) {
        const ok = window.confirm('Deleting the active project will close the current session and return you to Home. Continue?');
        if (!ok) return;
      }

      const res = await interviewAPI.deleteHistory(id);
      if (res.success) {
        setAllConversations(prev => prev.filter(c => c._id !== id));

        if (isActive) {
          // Stop any background polling immediately
          if (pollingIntervalRef.current) {
            clearInterval(pollingIntervalRef.current);
            pollingIntervalRef.current = null;
          }
          isPollingRef.current = false;
          buildCompleteDispatchedRef.current = false;

          // Clear in-memory state
          setMessages([]);
          setExistingBuildResult(null);
          setCurrentBuildResultUuid(null);
          setHasCodeGenerated(false);
          setIsGeneratingCode(false);
          setIsWorkspaceReady(false);
          setIsLoading(false);

          setActiveConversation('');
          setOpenHistoryView(false);
          navigate('/');
        }
      }
    } catch (e) {
      console.error('Delete failed', e);
    }
  };

  const handleRenameConversation = async (id: string, newTitle: string) => {
    try {
      const res = await interviewAPI.renameHistory(id, newTitle);
      if (res.success) {
        setAllConversations(prev => prev.map(c => c._id === id ? { ...c, title: newTitle, updatedAt: res.data.updatedAt } : c));
      }
    } catch (e) {
      console.error('Rename failed', e);
    }
  };

  // Debug logging for state changes
  useEffect(() => {
    console.log('🎯 BUILD PAGE STATE CHANGE:', {
      hasCodeGenerated,
      isWorkspaceReady,
      isLoading,
      buildProgress,
      shouldShowWorkspace: hasCodeGenerated && isWorkspaceReady, // FIXED: Both conditions required
      shouldShowLoading: !hasCodeGenerated || !isWorkspaceReady,  // FIXED: Show loading until both are ready
      renderLogic: 'MonacoWorkspace renders when hasCodeGenerated=true AND isWorkspaceReady=true'
    });
  }, [hasCodeGenerated, isWorkspaceReady, isLoading, buildProgress]);

  // Debug logging specifically for buildProgress changes
  useEffect(() => {
    console.log('📊 BUILD PROGRESS UPDATE:', buildProgress);
  }, [buildProgress]);

  const [shouldRefreshWorkspace, setShouldRefreshWorkspace] = useState(false);
  const [hasShownInitialMessages, setHasShownInitialMessages] = useState(false);
  const [chatHistoryRestored, setChatHistoryRestored] = useState(false);
  const [isRestoringFromDatabase, setIsRestoringFromDatabase] = useState(false);

  // Workspace layout state with persistence
  const [isWorkspaceCollapsed, setIsWorkspaceCollapsed] = useState(() => {
    const saved = localStorage.getItem('workspace-collapsed');
    return saved ? JSON.parse(saved) : false;
  });
  const [workspaceHeight, setWorkspaceHeight] = useState(() => {
    const saved = localStorage.getItem('workspace-height');
    return saved || 'calc(100vh - 200px)';
  });

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const previousUuidRef = useRef<string | undefined>(undefined);
  const pollingIntervalRef = useRef<ReturnType<typeof setInterval> | null>(null);
  const isPollingRef = useRef<boolean>(false);
  const buildCompleteDispatchedRef = useRef<boolean>(false);

  // Function to poll ECS workspace until files are uploaded and accessible
  const startECSWorkspacePolling = async (buildResult: any, interviewUuid: string) => {
    if (isPollingRef.current) {
      console.log('⏭️ POLLING: A polling session is already active. Skipping duplicate start.');
      return;
    }
    isPollingRef.current = true;
    console.log('🚀 POLLING START: ===== STARTING ECS WORKSPACE POLLING =====');
    console.log('🚀 POLLING START: Interview UUID:', interviewUuid);
    console.log('🚀 POLLING START: Current state:', { hasCodeGenerated, isWorkspaceReady, isLoading });
    console.log('🚀 POLLING START: Build result exists:', !!buildResult);
    console.log('🚀 POLLING START: Expected files:', buildResult.codeBlocks?.map((block: any) => block.filename).filter(Boolean) || []);
    console.log('🚀 POLLING START: Existing polling interval:', !!pollingIntervalRef.current);

    // Clear any existing polling
    if (pollingIntervalRef.current) {
      console.log('🚀 POLLING START: Clearing existing polling interval');
      clearInterval(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
    } else {
      console.log('🚀 POLLING START: No existing polling interval to clear');
    }

    // Check if workspace is already ready
    if (isWorkspaceReady) {
      console.log('🚀 POLLING START: Workspace already ready, skipping polling');
      return;
    }

    const pollInterval = 5000; // Poll every 5 seconds
    const maxAttempts = 60; // Poll for up to 5 minutes (60 * 5 seconds)
    let attempts = 0;
    let consecutiveErrors = 0;

    const pollWorkspace = async () => {
      attempts++;
      console.log(`🔍 POLLING ATTEMPT: ECS workspace polling attempt ${attempts}/${maxAttempts} for ${interviewUuid}`);
      console.log(`🔍 POLLING ATTEMPT: Current state:`, { hasCodeGenerated, isWorkspaceReady, isLoading });

      try {
        // Step 1: Check container status first
        console.log(`🔍 POLLING ATTEMPT: Making API call to getWorkspaceStatus for workspace_${interviewUuid}`);
        const containerStatus = await ecsWorkspaceAPI.getWorkspaceStatus(`workspace_${interviewUuid}`);

        console.log(`📊 POLLING RESPONSE: Full API response:`, containerStatus);
        console.log(`📊 POLLING RESPONSE: ECS container status:`, {
          success: containerStatus.success,
          containerStatus: containerStatus.data?.containerStatus,
          publicIp: containerStatus.data?.publicIp,
          isReady: containerStatus.data?.isReady,
          hasFiles: containerStatus.data?.hasFiles,
          fileCount: containerStatus.data?.fileCount,
          workspaceId: containerStatus.data?.workspaceId
        });

        // Tick when container is running or health check passes; files may follow
        if (containerStatus.success && (containerStatus.data?.isReady || containerStatus.data?.containerStatus === 'running')) {
          // Backend has verified both container is running AND files are available
                     const fileCount = containerStatus.data.fileCount || 0;
           const expectedFiles = buildResult.codeBlocks?.length || 0;
 
           console.log('🎉 ECS workspace is ready (health check passed)');
           if (containerStatus.data?.hasFiles) {
             console.log(`📁 Backend verified: ${fileCount} files available`);
           } else {
             console.log('⏳ Files not yet detected; will continue polling for files before showing Monaco');
           }
 
           // Clear the polling interval
          if (pollingIntervalRef.current) {
            clearInterval(pollingIntervalRef.current);
            pollingIntervalRef.current = null;
          }

                     // Tick workspace readiness immediately for UI, but only show Monaco when files exist (handled below)
           setBuildProgress(prev => ({ ...prev, workspaceCompleted: true }));

           // If files are available, proceed to workspace; else, keep polling for files
           if (containerStatus.data?.hasFiles) {
             console.log('⏳ ECS workspace ready with files, waiting 3 seconds to ensure stability...');
             setTimeout(() => {
               console.log('✅ ECS workspace: Setting isWorkspaceReady=true and proceeding to Monaco');
               setIsWorkspaceReady(true);
               setHasCodeGenerated(true);
               setIsLoading(false);
               setBuildProgress(prev => ({ ...prev, completed: true }));
               // Auto-minimize will be handled by centralized logic
             }, 3000);
           } else {
             // restart interval to keep polling for files presence
             if (!pollingIntervalRef.current) {
               pollingIntervalRef.current = setInterval(pollWorkspace, 5000);
             }
           }

          // Dispatch build completion event
          if (!buildCompleteDispatchedRef.current) {
            console.log('📤 ECS workspace polling: Dispatching build completion event');
            window.dispatchEvent(new CustomEvent('ecs-workspace-build-complete', {
              detail: {
                interviewUuid: interviewUuid,
                codeBlocksCount: expectedFiles,
                filesUploaded: fileCount,
                ecsReady: true,
                uploadSuccess: true,
                uploadedFiles: [], // Backend doesn't return file list in status
                containerStatus: containerStatus.data.containerStatus,
                publicIp: containerStatus.data.publicIp
              }
            }));
            buildCompleteDispatchedRef.current = true;
            console.log('✅ ECS workspace polling: Successfully dispatched build completion event');
          } else {
            console.log('⏭️ ECS workspace polling: Build completion event already dispatched, skipping');
          }

          return;
        } else {
          // More detailed logging for debugging
          if (!containerStatus.success) {
            console.log(`⏳ ECS workspace polling: API call failed`);
          } else if (!containerStatus.data?.isReady) {
            console.log(`⏳ ECS workspace polling: Container not ready - Status: ${containerStatus.data?.containerStatus}, PublicIp: ${containerStatus.data?.publicIp || 'null'}`);
          } else if (!containerStatus.data?.hasFiles) {
            console.log(`⏳ ECS workspace polling: Container ready but no files yet (${containerStatus.data?.fileCount || 0} files)`);
          }
        }

        // Reset consecutive errors on successful API call
        consecutiveErrors = 0;

        if (attempts >= maxAttempts) {
          console.log('⚠️ ECS workspace polling: Max attempts reached; keeping loading until files are detected');
          console.log('⚠️ Final status:', {
            containerStatus: containerStatus.data?.containerStatus,
            isReady: containerStatus.data?.isReady,
            hasFiles: containerStatus.data?.hasFiles,
            fileCount: containerStatus.data?.fileCount
          });

          // Clear the polling interval
          if (pollingIntervalRef.current) {
            clearInterval(pollingIntervalRef.current);
            pollingIntervalRef.current = null;
          }

          // Do not proceed to Monaco without files
          setBuildProgress(prev => ({ ...prev, workspaceCompleted: !!containerStatus.data?.isReady }));
          isPollingRef.current = false;
        }

      } catch (error: any) {
        consecutiveErrors++;
        console.error(`❌ ECS workspace polling error (attempt ${attempts}/${maxAttempts}, consecutive errors: ${consecutiveErrors}):`, error);

        // If we have too many consecutive errors, give up early
        if (consecutiveErrors >= 5 || attempts >= maxAttempts) {
          console.log('⚠️ ECS workspace polling: Too many errors or max attempts reached, showing workspace as fallback');
          console.log('⚠️ Error details:', {
            consecutiveErrors,
            attempts,
            maxAttempts,
            lastError: (error as any)?.message || 'Unknown error'
          });

          // Clear the polling interval
          if (pollingIntervalRef.current) {
            clearInterval(pollingIntervalRef.current);
            pollingIntervalRef.current = null;
          }

          // Do not proceed to Monaco without files even on error; keep workspace row as creating
          setBuildProgress(prev => ({ ...prev, workspaceCompleted: false }));
          isPollingRef.current = false;
        }
      }
    };

    // FIXED: Do an immediate check first in case workspace is already ready
    console.log('🚀 POLLING START: Doing immediate status check before starting interval');
    await pollWorkspace();

    // Only start interval polling if we haven't found the workspace ready yet
    if (attempts < maxAttempts && consecutiveErrors < 5 && !isWorkspaceReady) {
      console.log('🚀 POLLING START: Starting interval polling every', pollInterval, 'ms');
      console.log('🚀 POLLING START: Will poll for up to', maxAttempts, 'attempts (', (maxAttempts * pollInterval / 1000), 'seconds total)');
      pollingIntervalRef.current = setInterval(pollWorkspace, pollInterval);

      // Add a log every 30 seconds to show polling is still active
      const heartbeatInterval = setInterval(() => {
        if (pollingIntervalRef.current) {
          console.log('💓 POLLING HEARTBEAT: Still polling for ECS workspace accessibility... (attempt', attempts, '/', maxAttempts, ')');
        } else {
          clearInterval(heartbeatInterval);
        }
      }, 30000);
    } else {
      console.log('🚀 POLLING START: Not starting interval polling:', {
        attempts,
        maxAttempts,
        consecutiveErrors,
        isWorkspaceReady,
        reason: isWorkspaceReady ? 'Already ready' : 'Too many errors or attempts'
      });
      // Ensure we release the guard if we didn't start polling
      if (!pollingIntervalRef.current) {
        isPollingRef.current = false;
      }
    }
  };

  // FIXED: Immediate status check when component mounts with UUID
  useEffect(() => {
    if (uuid && !isWorkspaceReady) {
      console.log('🔍 IMMEDIATE CHECK: Component mounted with UUID, checking current status immediately');

      const immediateStatusCheck = async () => {
        try {
          // Check if build exists and is complete
          const buildExists = await llmAPI.checkBuildExists(uuid);
          console.log('🔍 IMMEDIATE CHECK: Build exists check:', buildExists);

          if (buildExists.success && buildExists.exists && buildExists.buildResult) {
            console.log('🔍 IMMEDIATE CHECK: Build exists, checking ECS workspace status');

            // Try to get ECS workspace status; if it 404s or fails, proactively create the workspace
            let workspaceStatus: any | null = null;
            let mustCreateWorkspace = false;
            try {
              workspaceStatus = await ecsWorkspaceAPI.getWorkspaceStatus(`workspace_${uuid}`);
              console.log('🔍 IMMEDIATE CHECK: Full workspace status response:', workspaceStatus);
              console.log('🔍 IMMEDIATE CHECK: Status details:', {
                success: workspaceStatus.success,
                containerStatus: workspaceStatus.data?.containerStatus,
                publicIp: workspaceStatus.data?.publicIp,
                isReady: workspaceStatus.data?.isReady,
                hasFiles: workspaceStatus.data?.hasFiles,
                fileCount: workspaceStatus.data?.fileCount
              });

              // Decide whether we need to create (missing/failed/stopped/no IP)
              if (!workspaceStatus.success || !workspaceStatus.data || ['failed', 'stopped'].includes(workspaceStatus.data?.containerStatus) || !workspaceStatus.data?.publicIp) {
                mustCreateWorkspace = true;
              }
            } catch (err: any) {
              console.log('❌ IMMEDIATE CHECK: Error getting workspace status, will create workspace. Error:', err?.message || err);
              mustCreateWorkspace = true;
            }

            if (mustCreateWorkspace) {
              try {
                console.log('🆕 IMMEDIATE CHECK: Creating ECS workspace since it is missing or not running');
                setBuildProgress(prev => ({ ...prev, workspaceCreating: true }));
                await ecsWorkspaceAPI.createWorkspace(`workspace_${uuid}`);
                console.log('✅ IMMEDIATE CHECK: Workspace creation requested, starting polling');
                startECSWorkspacePolling(buildExists.buildResult, uuid);
                setBuildProgress(prev => ({ ...prev, workspaceCreating: false }));
                return;
              } catch (createErr: any) {
                console.error('❌ IMMEDIATE CHECK: Failed to create ECS workspace:', createErr?.message || createErr);
                setBuildProgress(prev => ({ ...prev, workspaceCreating: false }));
              }
            }

            if (workspaceStatus && workspaceStatus.success && workspaceStatus.data?.isReady && workspaceStatus.data?.hasFiles) {
              console.log('✅ IMMEDIATE CHECK: Workspace is ready! Setting states immediately');
              setHasCodeGenerated(true);
              setIsWorkspaceReady(true);
              setIsLoading(false);
              setBuildProgress(prev => ({ ...prev, llmCompleted: true, workspaceCompleted: true, completed: true, workspaceCreating: false }));
              // Auto-minimize will be handled by centralized logic
            } else {
              console.log('⏳ IMMEDIATE CHECK: Workspace not ready, starting polling');
              if (buildExists.buildResult) {
                startECSWorkspacePolling(buildExists.buildResult, uuid);
                setBuildProgress(prev => ({ ...prev, workspaceCreating: false }));
              }
            }
          }
        } catch (error) {
          console.error('❌ IMMEDIATE CHECK: Error during immediate status check:', error);
        }
      };

      // Run immediate check after a short delay to let other effects run first
      const timeoutId = setTimeout(immediateStatusCheck, 1000);
      return () => clearTimeout(timeoutId);
    }
  }, [uuid, location.key, isWorkspaceReady]); // Only run when UUID changes or until workspace is ready

  // Safety timeout for stuck builds - force both states if stuck too long
  useEffect(() => {
    if ((!hasCodeGenerated || !isWorkspaceReady) && uuid) {
      console.log('⏰ Starting safety timeout for build completion state');

      const safetyTimeout = setTimeout(() => {
        console.log('⚠️ Safety timeout: Build still not complete after 5 minutes, forcing completion');
        setHasCodeGenerated(true);
        setIsWorkspaceReady(true);
        setBuildProgress(prev => ({ ...prev, llmCompleted: true, workspaceCompleted: true, completed: true }));
        // Auto-minimize will be handled by centralized logic
      }, 300000); // 5 minutes

      return () => clearTimeout(safetyTimeout);
    }
  }, [hasCodeGenerated, isWorkspaceReady, uuid]);

  // Cleanup polling on unmount
  useEffect(() => {
    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
        pollingIntervalRef.current = null;
      }
      isPollingRef.current = false;
    };
  }, []);

  // Listen for build completion events to set hasCodeGenerated when ECS workspace is ready
  useEffect(() => {
    const handleBuildComplete = (event: CustomEvent) => {
      const { interviewUuid: eventInterviewUuid, uploadSuccess, filesUploaded } = event.detail || {};
      console.log('🚨 BUILD COMPLETE EVENT: Build page received build complete event:', {
        eventInterviewUuid,
        uploadSuccess,
        filesUploaded,
        currentUuid: uuid,
        currentHasCodeGenerated: hasCodeGenerated,
        currentIsWorkspaceReady: isWorkspaceReady,
        eventSource: 'Should only come from polling after container accessibility verified'
      });

      if (eventInterviewUuid === uuid && uploadSuccess && filesUploaded > 0) {
        console.log('✅ BUILD COMPLETE EVENT: Build completion event received - ensuring workspace is ready');

        // FIXED: Only set workspace ready if event comes from polling (after container accessibility verified)
        if (!isWorkspaceReady) {
          console.log('✅ BUILD COMPLETE EVENT: Setting isWorkspaceReady=true after build completion event');
          setIsWorkspaceReady(true);
          setHasCodeGenerated(true);
          setIsLoading(false);
          setBuildProgress(prev => ({ ...prev, workspaceCompleted: true, completed: true }));
          // Auto-minimize will be handled by centralized logic
        } else {
          console.log('✅ BUILD COMPLETE EVENT: isWorkspaceReady already true');
        }
      } else {
        console.log('⏭️ BUILD COMPLETE EVENT: Ignoring event:', {
          uuidMatch: eventInterviewUuid === uuid,
          uploadSuccess,
          filesUploaded
        });
      }
    };

    window.addEventListener('ecs-workspace-build-complete', handleBuildComplete as EventListener);

    return () => {
      window.removeEventListener('ecs-workspace-build-complete', handleBuildComplete as EventListener);
    };
  }, [uuid, hasCodeGenerated]);

  // Get data from location state (passed from Interview page)
  const locationState = location.state as LocationState;
  const userPrompt = interviewData?.user?.prompt || locationState?.prompt;

  // Get project data in new format or convert from old format
  const projectData = interviewData?.projectData || locationState?.projectData;
  const userData = interviewData?.user || locationState?.user;

  // Function to extract files from LLM response
  const extractFilesFromResponse = (content: string): { [path: string]: string } => {
    const files: { [path: string]: string } = {};

    // Extract code blocks with file paths
    const codeBlockRegex = /```(?:(\w+)\s+)?(?:(?:###\s*)?([^\n]+\.[\w]+)[\s\n]*)?\n([\s\S]*?)```/g;
    let match;

    while ((match = codeBlockRegex.exec(content)) !== null) {
      const [, language, filePath, code] = match;

      if (filePath && code.trim()) {
        // Clean up the file path
        const cleanPath = filePath.replace(/^###\s*/, '').trim();
        files[cleanPath] = code.trim();
      }
    }

    return files;
  };

  // DEBUG: Log location state and LLM response
  console.log('🔍 Build page state:', {
    hasLocationState: !!locationState,
    locationStateKeys: locationState ? Object.keys(locationState) : [],
    hasLlmResponse: !!locationState?.llmResponse,
    llmResponseSuccess: locationState?.llmResponse?.success,
    hasExtractedContent: !!locationState?.llmResponse?.extractedContent,
    extractedContentKeys: locationState?.llmResponse?.extractedContent ? Object.keys(locationState.llmResponse.extractedContent) : [],
    extractedContent: locationState?.llmResponse?.extractedContent,
    messagesLength: messages.length,
    uuid,
    hasShownInitialMessages
  });

  // FIXED: Load chat history from database OR wait for initial build messages
  useEffect(() => {
    if (!uuid) return;

    const loadChatHistory = async () => {
      try {
        console.log('💬 Loading chat history from database for UUID:', uuid);
        setIsRestoringFromDatabase(true); // Prevent saves during restoration

        const data = await llmAPI.checkBuildExists(uuid);

        if (data.success && data.exists && data.buildResult?.chatHistory?.length > 0) {
          console.log('✅ Found existing chat history in database:', data.buildResult.chatHistory.length, 'messages');
          const restoredMessages = data.buildResult.chatHistory.map((msg: any) => ({
            ...msg,
            timestamp: new Date(msg.timestamp)
          }));
          setMessages(restoredMessages);
          setHasShownInitialMessages(true);
          setChatHistoryRestored(true);
          setExistingBuildResult(data.buildResult);
          // For existing builds with chat history, set LLM as completed and check ECS workspace
          console.log('🔍 Frontend: Chat history restored, setting LLM completed and checking ECS workspace status...');
          setHasCodeGenerated(true); // LLM response already exists
          setBuildProgress(prev => ({ ...prev, llmGenerating: false, llmCompleted: true }));

          // Check if ECS workspace has files before setting workspace ready
          try {
            const workspaceStructure = await ecsWorkspaceAPI.getStructure(`workspace_${uuid}`);
            console.log('🔍 Frontend: ECS workspace structure check for existing build');

            if (workspaceStructure.success && workspaceStructure.data.structure) {
              const files = workspaceStructure.data.structure;
              const fileCount = Object.keys(files).length;
              console.log('✅ Frontend: ECS workspace has', fileCount, 'files for existing build');

                                  if (fileCount > 0) {
                      console.log('✅ Frontend: Setting isWorkspaceReady=true - files are available');
                      setIsWorkspaceReady(true);
                      setIsLoading(false);
                      setBuildProgress(prev => ({ ...prev, workspaceCreating: false, workspaceCompleted: true, completed: true }));
                      // Auto-minimize will be handled by centralized logic
                    } else {
                console.log('⏳ Frontend: ECS workspace empty, starting polling for existing build (loadChatHistory)');
                setBuildProgress(prev => ({ ...prev, workspaceCreating: true, workspaceCompleted: false }));
                // Start polling for existing builds too
                if (data.buildResult) {
                  startECSWorkspacePolling(data.buildResult, uuid!);
                }
              }
            } else {
              console.log('⏳ Frontend: Could not get workspace structure, starting polling for existing build');
              setBuildProgress(prev => ({ ...prev, workspaceCreating: true, workspaceCompleted: false }));
              if (data.buildResult) {
                startECSWorkspacePolling(data.buildResult, uuid!);
              }
            }
          } catch (workspaceError) {
            console.log('⚠️ Frontend: Could not check ECS workspace structure, starting polling as fallback (loadChatHistory)');
            setBuildProgress(prev => ({ ...prev, workspaceCreating: true, workspaceCompleted: false }));
            if (data.buildResult) {
              startECSWorkspacePolling(data.buildResult, uuid!);
            }
          }

          setIsGeneratingCode(false);
          setCurrentBuildResultUuid(data.buildResult.uuid); // Set the UUID for future saves
          console.log('✅ Chat history loaded from database');

          // Allow saves again after a short delay to ensure restoration is complete
          setTimeout(() => {
            setIsRestoringFromDatabase(false);
          }, 1000);
          return;
        }

        console.log('📝 No existing chat history found - this is a new build');
        setIsRestoringFromDatabase(false);
        // For new builds, we'll wait for LLM response or other sources to create initial messages
        // Don't create any messages here - let the other useEffects handle initial message creation

      } catch (error) {
        console.error('❌ Error loading chat history:', error);
        setIsRestoringFromDatabase(false);
      }
    };

    loadChatHistory();
  }, [uuid, location.key]); // Only depend on UUID

  // REMOVED: This useEffect was causing conflicts with the unified build response handling
  // The chat history is now properly handled in the buildProject function

  // FALLBACK: Add a test message after 300 seconds if no messages are shown
  // BUT only if we're not restoring from database and don't have initial messages
  useEffect(() => {
    const timer = setTimeout(() => {
      if (messages.length === 0 && !hasShownInitialMessages && !existingBuildResult && !chatHistoryRestored && !isGeneratingCode) {
        console.log('🚨 FALLBACK: No messages shown after 5 seconds, adding test message');
        setMessages([{
          id: 'fallback-test',
          type: 'ai',
          content: '🧪 FALLBACK: Build page loaded but no LLM messages were displayed. This indicates an issue with the message flow.',
          timestamp: new Date()
        }]);
      } else {
        console.log('⏭️ FALLBACK: Skipping fallback message because:', {
          messagesLength: messages.length,
          hasShownInitialMessages,
          hasExistingBuildResult: !!existingBuildResult,
          chatHistoryRestored,
          isGeneratingCode
        });
      }
    }, 300000); // Increased to 300 seconds to give more time for unified build

    return () => clearTimeout(timer);
  }, [messages.length, hasShownInitialMessages, existingBuildResult, chatHistoryRestored, isGeneratingCode]);



  // Debounced save to avoid conflicts - only save when messages change and we're not submitting or restoring
  useEffect(() => {
    // Don't save if we're still restoring chat history or if we just loaded from database
    if (currentBuildResultUuid && messages.length > 0 && !isSubmitting && !chatHistoryRestored && !isRestoringFromDatabase && hasShownInitialMessages) {
      const timeoutId = setTimeout(() => {
        console.log('💾 Debounced save - chat history after message changes:', {
          buildResultUuid: currentBuildResultUuid,
          messageCount: messages.length,
          isSubmitting,
          chatHistoryRestored,
          isRestoringFromDatabase,
          hasShownInitialMessages
        });

        saveChatHistory(currentBuildResultUuid, messages).catch(error => {
          console.error('❌ Error in debounced save:', error);
        });
      }, 3000); // 3 second delay to avoid conflicts with backend

      return () => clearTimeout(timeoutId);
    }
  }, [currentBuildResultUuid, messages, isSubmitting, chatHistoryRestored, isRestoringFromDatabase, hasShownInitialMessages]);

  // Auto-scroll to bottom of messages
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    console.log('📜 Messages changed, new length:', messages.length);
    console.log('📜 Message contents:', messages.map(m => `${m.type}: ${m.content.substring(0, 30)}...`));
    scrollToBottom();
  }, [messages]);

  // Load interview data if UUID is provided
  useEffect(() => {
    let isMounted = true;

    // Clear previous state when UUID changes (but only if UUID actually changed)
    console.log('🔄 Frontend: UUID changed to:', uuid, 'previous:', previousUuidRef.current);

          // Only clear state if this is a different UUID
      const shouldClearState = previousUuidRef.current !== uuid;

      if (shouldClearState) {
        console.log('🧹 Clearing state for new UUID');
        setMessages([]);
        setExistingBuildResult(null);
        setCurrentBuildResultUuid(null);
        setHasCodeGenerated(false);
        setIsGeneratingCode(false);
        setError(null);
        setHasShownInitialMessages(false);
        setChatHistoryRestored(false); // IMPORTANT: Reset this for new builds
        setIsRestoringFromDatabase(false); // Reset restoration flag
        setIsWorkspaceReady(false); // Reset workspace ready state
        // Don't reset chatbox minimized state here - let the auto-minimize logic handle it
        // setIsChatboxMinimized(false); // Reset chatbox minimized state for new builds
        hasAutoMinimizedRef.current = false; // Reset auto-minimize ref for new builds
        buildCompleteDispatchedRef.current = false; // Reset dispatch guard for new session

      // Clear any existing ECS polling from previous session
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
        pollingIntervalRef.current = null;
      }
      isPollingRef.current = false;

      // Update the ref to track current UUID
      previousUuidRef.current = uuid;
    } else {
      console.log('⏭️ Keeping existing state for same UUID');
    }



    const loadInterviewData = async () => {
      if (!isMounted) return;

      if (!uuid) {
        setIsLoading(false);
        // Check if we have data from location state, otherwise show error
        if (!userPrompt && !locationState?.prompt) {
          setError('No interview session found. Please start from the interview page to generate code.');
          return;
        }
        // Start code generation immediately if no UUID but we have prompt data
        if (userPrompt && !isGeneratingCode && !hasCodeGenerated) {
          handleInitialCodeGeneration();
        }
        return;
      }

      try {
        setIsLoading(true);
        setError(null);
        const response = await interviewAPI.get(uuid);

        if (!isMounted) return;

        if (response.success) {
          setInterviewData(response.data);
          // Interview config loaded; switch from initial spinner to main content
          setIsLoading(false);

          // Check if this is a fresh build request from interview page
          const shouldStartBuild = locationState?.shouldStartBuild;

          if (shouldStartBuild) {
            console.log('🚀 Frontend: Fresh build request from interview page, starting build immediately...');

            // Start the build process immediately for better UX
            const combinedData = {
              ...response.data,
              locationState: locationState
            };

            const effectivePrompt = locationState?.prompt || response.data?.user?.prompt || 'Generate project based on interview responses';
            buildProject(effectivePrompt, combinedData?.projectData || response.data?.projectData, combinedData?.user || response.data?.user);
          } else {
            // Check for existing build in database before starting new build
            console.log('🔍 Frontend: Checking for existing build in database before starting new build...');
            console.log('🔍 Frontend: Current state:', {
              uuid,
              isGeneratingCode,
              hasCodeGenerated,
              existingBuildResult: !!existingBuildResult,
              chatHistoryRestored
            });

            try {
              const existingBuildData = await llmAPI.checkBuildExists(uuid);

              console.log('🔍 Frontend: Existing build check result:', existingBuildData);

              if (existingBuildData.success && existingBuildData.exists) {
                console.log('✅ Frontend: Found existing build in database, skipping new build');
                console.log('✅ Frontend: Existing build has chat history:', existingBuildData.buildResult?.chatHistory?.length || 0, 'messages');
                // FIXED: For existing builds, verify ECS workspace before setting hasCodeGenerated
                console.log('🔍 Frontend: Existing build in database, checking ECS workspace status...');

                // For existing builds, set LLM as completed and check ECS workspace
                setHasCodeGenerated(true); // LLM response already exists
                setBuildProgress(prev => ({ ...prev, llmGenerating: false, llmCompleted: true }));

                // Check if ECS workspace has files before setting workspace ready
                try {
                  const workspaceStructure = await ecsWorkspaceAPI.getStructure(`workspace_${uuid}`);
                  console.log('🔍 Frontend: ECS workspace structure check for existing build');

                  if (workspaceStructure.success && workspaceStructure.data.structure) {
                    const files = workspaceStructure.data.structure;
                    const fileCount = Object.keys(files).length;
                    console.log('✅ Frontend: ECS workspace has', fileCount, 'files for existing build');

                    if (fileCount > 0) {
                      console.log('✅ Frontend: Setting isWorkspaceReady=true - files are available');
                      setIsWorkspaceReady(true);
                      setIsLoading(false);
                      setBuildProgress(prev => ({ ...prev, workspaceCreating: false, workspaceCompleted: true, completed: true }));
                      // Auto-minimize will be handled by centralized logic
                    } else {
                      console.log('⏳ Frontend: ECS workspace empty, starting polling for existing build (loadChatHistory)');
                      setBuildProgress(prev => ({ ...prev, workspaceCreating: true, workspaceCompleted: false }));
                      // Start polling for existing builds too
                      if (existingBuildData.buildResult) {
                        startECSWorkspacePolling(existingBuildData.buildResult, uuid!);
                      }
                    }
                  } else {
                    console.log('⏳ Frontend: Could not get workspace structure, starting polling for existing build');
                    setBuildProgress(prev => ({ ...prev, workspaceCreating: true, workspaceCompleted: false }));
                    if (existingBuildData.buildResult) {
                      startECSWorkspacePolling(existingBuildData.buildResult, uuid!);
                    }
                  }
                } catch (workspaceError) {
                  console.log('⚠️ Frontend: Could not check ECS workspace structure, starting polling as fallback (loadInterviewData)');
                  setBuildProgress(prev => ({ ...prev, workspaceCreating: true, workspaceCompleted: false }));
                  if (existingBuildData.buildResult) {
                    startECSWorkspacePolling(existingBuildData.buildResult, uuid!);
                  }
                }

                setIsGeneratingCode(false);
                // Note: Keep isWorkspaceReady as false until Monaco workspace is actually ready

                // Set project structure if available
                if (existingBuildData.buildResult?.projectStructure) {
                  setProjectStructure(existingBuildData.buildResult.projectStructure);
                }

                // The chat history will be loaded by the loadChatHistory useEffect
                console.log('✅ Frontend: Existing build setup complete, waiting for workspace to be ready');
              } else if (!isGeneratingCode) {
                console.log('✅ Frontend: No existing build found, starting new build with archive for interview:', uuid);

                const combinedData = {
                  ...response.data,
                  locationState: locationState
                };

                const effectivePrompt = locationState?.prompt || response.data?.user?.prompt || 'Generate project based on interview responses';
                buildProject(effectivePrompt, combinedData?.projectData || response.data?.projectData, combinedData?.user || response.data?.user);
              } else {
                console.log('⏭️ Frontend: Skipping build - isGeneratingCode:', isGeneratingCode);
              }
            } catch (checkError) {
              console.error('❌ Error checking for existing build:', checkError);
              // If check fails, proceed with new build as fallback
              if (!isGeneratingCode) {
                console.log('⚠️ Frontend: Check failed, proceeding with new build as fallback');
                const combinedData = {
                  ...response.data,
                  locationState: locationState
                };
                const effectivePrompt = locationState?.prompt || response.data?.user?.prompt || 'Generate project based on interview responses';
                buildProject(effectivePrompt, combinedData?.projectData || response.data?.projectData, combinedData?.user || response.data?.user);
              }
            }
          }
        } else {
          setError('Failed to load interview configuration');
        }
      } catch (error: any) {
        console.error('Error loading interview data:', error);
        if (isMounted) {
          setError(error.response?.data?.message || 'Failed to load interview configuration');
        }
      } finally {
        if (isMounted) {
          // Only set loading to false if we're not generating code AND not building
          // Keep loading state during build process until Monaco workspace is ready
          if (!isGeneratingCode && !buildProgress.llmGenerating && !buildProgress.workspaceCreating && !hasCodeGenerated) {
            setIsLoading(false);
          } else {
            console.log('⏳ Frontend: Keeping loading state - build in progress or code generated but workspace not ready');
          }
        }
      }
    };

    loadInterviewData();

    return () => {
      isMounted = false;
    };
  }, [uuid, location.key]); // Remove userPrompt dependency to prevent multiple runs



  // Save chat history to database (debounced to avoid conflicts)
  const saveChatHistory = async (buildResultUuid: string, chatHistory: ChatMessage[]) => {
    try {
      console.log('💾 Saving chat history:', {
        buildResultUuid,
        messageCount: chatHistory.length,
        messageTypes: chatHistory.map(m => m.type),
        lastMessage: chatHistory[chatHistory.length - 1]?.content?.substring(0, 50) + '...'
      });
      await chatAPI.updateChatHistory(buildResultUuid, chatHistory);
      console.log('✅ Chat history saved successfully');
    } catch (error) {
      console.error('❌ Error saving chat history:', error);
    }
  };

  // Simple function to create messages from extracted content
  const createMessagesFromExtractedContent = (extractedContent: any): ChatMessage[] => {
    console.log('📝 Creating messages from extracted content:', {
      hasDescription: !!extractedContent.description,
      hasDeploymentInstructions: !!extractedContent.deploymentInstructions,
      hasAdditionalSections: !!extractedContent.additionalSections
    });

    const messages: ChatMessage[] = [];
    let messageId = 1;

    // 1. Initial welcome message
    messages.push({
      id: messageId.toString(),
      type: 'ai',
      content: '🚀 Starting project generation...',
      timestamp: new Date()
    });
    messageId++;

    // 2. Success message with description (if available)
    if (extractedContent.description) {
      messages.push({
        id: messageId.toString(),
        type: 'ai',
        content: `🎉 Project generated successfully!\n\n${extractedContent.description}`,
        timestamp: new Date()
      });
      messageId++;

      // Set project structure if available
      if (extractedContent.projectStructure) {
        setProjectStructure(extractedContent.projectStructure);
      }
    }

    // 3. Additional sections (if any)
    if (extractedContent.additionalSections && Object.keys(extractedContent.additionalSections).length > 0) {
      for (const [sectionName, sectionContent] of Object.entries(extractedContent.additionalSections)) {
        messages.push({
          id: messageId.toString(),
          type: 'ai',
          content: `📋 **${sectionName}**\n\n${sectionContent}`,
          timestamp: new Date()
        });
        messageId++;
      }
    }

    // 4. Deployment instructions (if available)
    if (extractedContent.deploymentInstructions) {
      messages.push({
        id: messageId.toString(),
        type: 'ai',
        content: `🚀 **Ready to Deploy!**\n\n${extractedContent.deploymentInstructions}`,
        timestamp: new Date()
      });
      messageId++;
    }

    // 5. Final completion message
    messages.push({
      id: messageId.toString(),
      type: 'ai',
      content: '✅ Project generation complete! Your code is ready for review and deployment.',
      timestamp: new Date()
    });

    console.log('✅ Created', messages.length, 'messages from extracted content');
    return messages;
  };

  // Handle initial code generation when component loads
  const handleInitialCodeGeneration = async (data?: InterviewData) => {
    console.log('🚀 handleInitialCodeGeneration called with:', {
      isGeneratingCode,
      hasCodeGenerated,
      existingBuildResult: !!existingBuildResult,
      messagesLength: messages.length,
      hasLocationState: !!locationState,
      hasData: !!data,
      hasShownInitialMessages
    });

    if (isGeneratingCode || hasCodeGenerated) {
      console.log('❌ Skipping code generation - already generating or completed');
      return;
    }

    // FIXED: Don't start generation if we have an existing build with chat history
    if (existingBuildResult) {
      console.log('❌ Skipping code generation - existing build found with chat history');
      return;
    }

    console.log('✅ Starting initial code generation...');
    setIsGeneratingCode(true);
    setIsLoading(true); // Keep loading state during build process
    setIsWorkspaceReady(false); // Reset workspace ready state
    setIsChatboxMinimized(false); // Reset chatbox minimized state for new builds
    hasAutoMinimizedRef.current = false; // Reset auto-minimize ref for new builds

    // Check if we have LLM response data from the interview
    // ENHANCED: Check both direct locationState and data.locationState (passed from loadInterviewData)
    const effectiveLocationState = locationState || (data as any)?.locationState;
    const llmResponse = effectiveLocationState?.llmResponse;
    const llmError = effectiveLocationState?.llmError;

    console.log('🔍 LLM Response check:', {
      hasLocationState: !!locationState,
      hasDataLocationState: !!(data as any)?.locationState,
      hasEffectiveLocationState: !!effectiveLocationState,
      hasLlmResponse: !!llmResponse,
      llmSuccess: llmResponse?.success,
      hasExtractedContent: !!llmResponse?.extractedContent,
      llmError,
      extractedContentKeys: llmResponse?.extractedContent ? Object.keys(llmResponse.extractedContent) : [],
      hasShownInitialMessages
    });

    if (llmError && !existingBuildResult && !chatHistoryRestored) {
      const errorMessage = {
        id: '1',
        type: 'ai' as const,
        content: `Welcome to the Build environment! ⚠️ There was an issue generating content: ${llmError}. Using fallback generation...`,
        timestamp: new Date()
      };
      setMessages([errorMessage]);
      console.log('📝 Set error message');
    } else if (llmError && existingBuildResult) {
      console.log('📝 SKIP: Not setting error message - existing build result found with chat history');

      // Save error message immediately if we have a buildResultUuid later
      // This will be handled in fetchCodeStructure
    } else if (llmResponse?.success && llmResponse.extractedContent && !hasShownInitialMessages && !existingBuildResult && !chatHistoryRestored) {
      // SIMPLIFIED: Create messages from LLM extracted content (only for new builds)
      // BUT only if we don't have existing chat history restored
      console.log('📝 Creating messages from LLM extracted content (NEW BUILD)');
      console.log('📝 Extracted content:', llmResponse.extractedContent);

      // Mark that we're showing initial messages to prevent duplicates
      setHasShownInitialMessages(true);

      // Create all messages from extracted content immediately
      const extractedMessages = createMessagesFromExtractedContent(llmResponse.extractedContent);
      setMessages(extractedMessages);
      console.log('✅ Set', extractedMessages.length, 'messages from extracted content');

      // Extract and populate Monaco workspace with initial files
      if (llmResponse.extractedContent?.codeBlocks && uuid) {
        const initialFiles: { [path: string]: string } = {};

        llmResponse.extractedContent.codeBlocks.forEach((block: any) => {
          if (block.filename && block.content) {
            initialFiles[block.filename] = block.content;
          }
        });

        if (Object.keys(initialFiles).length > 0) {
          console.log('📁 Populating Monaco workspace with initial files:', Object.keys(initialFiles));

          // Dispatch event to Monaco workspace
          window.dispatchEvent(new CustomEvent('monaco-workspace-update', {
            detail: { files: initialFiles }
          }));
        }
      }
    } else if (existingBuildResult) {
      console.log('📝 SKIP: Not creating messages from LLM content - existing build result found with chat history');
    } else if (!existingBuildResult && !chatHistoryRestored) {
      const welcomeMessage = {
        id: '1',
        type: 'ai' as const,
        content: 'Welcome to the Build environment! I\'m starting to generate your project based on your requirements...',
        timestamp: new Date()
      };
      setMessages([welcomeMessage]);
      console.log('📝 Set welcome message');

      // Save welcome message immediately if we have a buildResultUuid later
      // This will be handled in fetchCodeStructure
    }

    // FALLBACK: Ensure at least one message is always shown
    // BUT only if we don't have existing chat history restored
    if (messages.length === 0 && !existingBuildResult && !chatHistoryRestored) {
      console.log('🚨 FALLBACK: No messages found, adding fallback message');
      const fallbackMessage = {
        id: 'fallback-1',
        type: 'ai' as const,
        content: '🚀 Initializing your project build environment...',
        timestamp: new Date()
      };
      setMessages([fallbackMessage]);
    } else if (messages.length === 0 && existingBuildResult) {
      console.log('🚨 FALLBACK: No messages but existing build result found - chat history should be restored');
    }

    try {
      const effectivePrompt = data?.user?.prompt || userPrompt || 'Web application';

      // If we already have LLM response data, skip the generation and proceed to fetchCodeStructure
      if (llmResponse?.success && llmResponse.extractedContent) {
        console.log('Using existing LLM response data, proceeding to fetch code structure...');

        // Proceed directly to buildProject which will handle progressive messages
        setTimeout(() => {
          buildProject(effectivePrompt, data?.projectData || projectData, data?.user || userData);
          setIsGeneratingCode(false);
          // FIXED: Don't set hasCodeGenerated=true here - let buildProject handle it based on ECS workspace status
        }, 500);

        return;
      }

      // Start the chat stream for generation progress (fallback)
      const response = await buildAPI.generateCode({
        interviewUuid: uuid,
        prompt: effectivePrompt,
        projectData: data?.projectData || projectData,
        userData: data?.user || userData
      });

      if (!response.ok) {
        throw new Error('Failed to generate code');
      }

      const reader = response.body?.getReader();
      const decoder = new TextDecoder();

      if (reader) {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value);
          const lines = chunk.split('\n');

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const data = JSON.parse(line.slice(6));

                if (data.type === 'ai' && data.content) {
                  const messageId = `${Date.now()}-${Math.random()}`;
                  console.log('Adding AI message:', data.content);
                  const newMessage = {
                    id: messageId,
                    type: 'ai' as const,
                    content: data.content,
                    timestamp: new Date()
                  };

                  setMessages(prev => {
                    // Check if this message already exists (duplicate prevention)
                    const isDuplicate = prev.some(msg =>
                      msg.content === data.content &&
                      msg.type === 'ai' &&
                      Math.abs(new Date().getTime() - msg.timestamp.getTime()) < 5000 // within 5 seconds
                    );

                    if (isDuplicate) {
                      console.log('Skipping duplicate message:', data.content);
                      return prev;
                    }

                    const updatedMessages = [...prev, newMessage];

                    // Save to database immediately if we have a build result UUID
                    if (currentBuildResultUuid) {
                      console.log('💾 Saving streaming message to database');
                      saveChatHistory(currentBuildResultUuid, updatedMessages).catch(error => {
                        console.error('❌ Error saving streaming message:', error);
                      });
                    }

                    return updatedMessages;
                  });
                } else if (data.success && data.message === 'Code generation process completed') {
                  // Generation completed, now build project
                  console.log('Generation completed, building project...');
                  buildProject(effectivePrompt, data?.projectData || projectData, data?.user || userData);
                }
              } catch (e) {
                console.error('Error parsing SSE data:', e);
              }
            }
          }
        }
      }
    } catch (error) {
      console.error('Error generating code:', error);
      const errorMessage = {
        id: Date.now().toString(),
        type: 'ai' as const,
        content: 'Sorry, there was an error generating your code. Please try again.',
        timestamp: new Date()
      };

      setMessages(prev => {
        const updatedMessages = [...prev, errorMessage];

        // Save error message to database
        if (currentBuildResultUuid) {
          console.log('💾 Saving error message to database');
          saveChatHistory(currentBuildResultUuid, updatedMessages).catch(error => {
            console.error('❌ Error saving error message:', error);
          });
        }

        return updatedMessages;
      });
    } finally {
      setIsGeneratingCode(false);
    }
  };

  // Build project using unified build API
  const buildProject = async (prompt: string, projectData?: any, userData?: any) => {
    // Declare thinking message outside try block so it's accessible in catch
    let thinkingMessage: ChatMessage | null = null;

    try {
      console.log('📡 Frontend: Building with unified build API...');
      console.log('📡 Frontend: Interview UUID:', uuid);
      console.log('📡 Frontend: Prompt:', prompt);

      // GUARD: Check if we already have an existing build to prevent overwriting
      if (existingBuildResult || chatHistoryRestored) {
        console.log('🛡️ Frontend: GUARD - Existing build detected, skipping unified build to prevent overwrite');
        console.log('🛡️ Frontend: existingBuildResult:', !!existingBuildResult);
        console.log('🛡️ Frontend: chatHistoryRestored:', chatHistoryRestored);
        return;
      }

      // Double-check with database before proceeding
      console.log('🔍 Frontend: Double-checking database for existing build before unified build...');
      try {
        const checkData = await llmAPI.checkBuildExists(uuid!);

        if (checkData.success && checkData.exists) {
          console.log('🛡️ Frontend: GUARD - Database check found existing build, aborting unified build');
          console.log('🛡️ Frontend: Existing build has', checkData.buildResult?.chatHistory?.length || 0, 'chat messages');

          // FIXED: For existing builds, verify ECS workspace has files before setting states
          console.log('🔍 Frontend: Existing build found, checking ECS workspace files...');

          // Check if ECS workspace has files before setting states
          try {
            const workspaceStructure = await ecsWorkspaceAPI.getStructure(`workspace_${uuid}`);
            console.log('🔍 Frontend: ECS workspace structure for existing build');

            if (workspaceStructure.success && workspaceStructure.data.structure) {
              const files = workspaceStructure.data.structure;
              const fileCount = Object.keys(files).length;
              console.log('✅ Frontend: ECS workspace has', fileCount, 'files for existing build');

              if (fileCount > 0) {
                console.log('✅ Frontend: ECS workspace ready for existing build - files available');
                setHasCodeGenerated(true);
                setIsWorkspaceReady(true);
                setBuildProgress(prev => ({ ...prev, llmCompleted: true, workspaceCompleted: true, completed: true }));
                // Auto-minimize will be handled by centralized logic
              } else {
                console.log('⏳ Frontend: ECS workspace empty for existing build, starting polling');
                setHasCodeGenerated(true); // LLM is done for existing builds
                setBuildProgress(prev => ({ ...prev, llmCompleted: true, workspaceCreating: true, workspaceCompleted: false }));
                // Start polling for existing builds too
                if (checkData.buildResult) {
                  startECSWorkspacePolling(checkData.buildResult, uuid!);
                }
              }
            } else {
              console.log('⏳ Frontend: Could not get workspace structure for existing build, starting polling');
              setHasCodeGenerated(true); // LLM is done for existing builds
              setBuildProgress(prev => ({ ...prev, llmCompleted: true, workspaceCreating: true, workspaceCompleted: false }));
              if (checkData.buildResult) {
                startECSWorkspacePolling(checkData.buildResult, uuid!);
              }
            }
          } catch (workspaceError) {
            console.log('⚠️ Frontend: Could not check ECS workspace structure for existing build, starting polling as fallback');
            setHasCodeGenerated(true); // LLM is done for existing builds
            setBuildProgress(prev => ({ ...prev, llmCompleted: true, workspaceCreating: true, workspaceCompleted: false }));
            if (checkData.buildResult) {
              startECSWorkspacePolling(checkData.buildResult, uuid!);
            }
          }

          setIsGeneratingCode(false);
          if (checkData.buildResult?.projectStructure) {
            setProjectStructure(checkData.buildResult.projectStructure);
          }
          return;
        }
      } catch (checkError) {
        console.warn('⚠️ Frontend: Database check failed, proceeding with unified build:', checkError);
      }

      // Add "AI is thinking..." message before starting the build
      thinkingMessage = {
        id: `thinking-${Date.now()}`,
        type: 'ai',
        content: '🤖 AI is analyzing your requirements and generating code...',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, thinkingMessage!]);

      // Set initial progress states - start with LLM generation only
      setBuildProgress({
        llmGenerating: true,
        llmCompleted: false,
        workspaceCreating: false, // Don't start workspace creation until LLM is done
        workspaceCompleted: false,
        filesUploading: false,
        completed: false
      });

      // Reset chatbox minimized state for new builds
      setIsChatboxMinimized(false);
      hasAutoMinimizedRef.current = false; // Reset auto-minimize ref for new builds

      const response = await buildAPI.unifiedBuild({
        interviewUuid: uuid,
        prompt: prompt,
        projectData: projectData,
        userData: userData
      });

      console.log('📡 Frontend: Unified build response received:', {
        success: response.success,
        hasBuildResult: !!response.buildResult,
        buildResultUuid: response.buildResult?.uuid,
        hasProjectStructure: !!response.buildResult?.projectStructure,
        hasWorkspace: !!response.workspace,
        workspaceStatus: response.workspace?.status,
        filesUploaded: response.workspace?.filesUploaded,
        ecsReady: response.workspace?.ecsReady,
        uploadSuccess: response.workspace?.uploadSuccess,
        uploadedFiles: response.workspace?.uploadedFiles,
        workflow: response.metadata?.workflow
      });

      // Remove the thinking message now that we have a response
      setMessages(prev => prev.filter(msg => msg.id !== thinkingMessage!.id));

      // Mark LLM as completed since we got a response
      setBuildProgress(prev => ({ ...prev, llmGenerating: false, llmCompleted: true }));
      // NOTE: Don't set hasCodeGenerated yet - wait until we check container status

      if (response.success && response.buildResult?.projectStructure) {
        console.log('Code structure built successfully:', response.buildResult.projectStructure);
        setProjectStructure(response.buildResult.projectStructure);

        // FIXED: Check backend response for file upload status FIRST
        console.log('🔍 Frontend: Checking backend response for file upload status...');
        console.log('🔍 Frontend: Workspace response:', {
          uploadSuccess: response.workspace?.uploadSuccess,
          filesUploaded: response.workspace?.filesUploaded,
          ecsReady: response.workspace?.ecsReady,
          uploadedFiles: response.workspace?.uploadedFiles
        });

        // FIXED: Set LLM as completed since we got a response
        setHasCodeGenerated(true);
        setBuildProgress(prev => ({ ...prev, llmGenerating: false, llmCompleted: true }));

        // FIXED: Always start polling to verify container accessibility, regardless of file upload status
        setBuildProgress(prev => ({ ...prev, workspaceCreating: true, workspaceCompleted: false }));

        if (response.workspace?.uploadSuccess && response.workspace?.filesUploaded > 0) {
          console.log('✅ Frontend: Backend confirms files uploaded successfully!');
          console.log('✅ Frontend: Files uploaded:', response.workspace.filesUploaded);
          console.log('✅ Frontend: Uploaded files:', response.workspace.uploadedFiles);
          console.log('⏳ Frontend: Files uploaded but must verify ECS container accessibility');
        } else {
          console.log('⏳ Frontend: Backend indicates files not uploaded yet');
          console.log('⏳ Frontend: Upload status:', {
            uploadSuccess: response.workspace?.uploadSuccess,
            filesUploaded: response.workspace?.filesUploaded,
            ecsReady: response.workspace?.ecsReady
          });
        }

        // FIXED: Always start polling to verify container accessibility
        if (response.buildResult) {
          console.log('🚀 AUTO POLLING: Starting polling after build response (regardless of file upload status)');
          console.log('🚀 AUTO POLLING: Current state before polling:', {
            hasCodeGenerated,
            isWorkspaceReady,
            isLoading,
            buildResultExists: !!response.buildResult,
            filesUploaded: response.workspace?.filesUploaded || 0
          });
          startECSWorkspacePolling(response.buildResult, uuid!);
        } else {
          console.log('⚠️ AUTO POLLING: No buildResult to start polling with');
        }

        // ENHANCED: Set the buildResultUuid and handle progressive message display
        if (response.buildResult?.uuid) {
          console.log('💾 Setting BuildResult UUID:', response.buildResult.uuid);
          setCurrentBuildResultUuid(response.buildResult.uuid);

          // Check if the unified build response already includes chat history
          if (response.buildResult.chatHistory && response.buildResult.chatHistory.length > 0) {
            console.log('✅ Using chat history from unified build response:', response.buildResult.chatHistory.length, 'messages');
            const messagesFromResponse = response.buildResult.chatHistory.map((msg: any) => ({
              ...msg,
              timestamp: new Date(msg.timestamp)
            }));
            setMessages(messagesFromResponse);
            setHasShownInitialMessages(true);
            console.log('✅ Set', messagesFromResponse.length, 'messages from unified build response');
          } else if (!existingBuildResult && !chatHistoryRestored) {
            // Fallback: Create messages from extracted content if no chat history in response
            console.log('📝 Creating messages from unified build result (fallback)...');
            const messagesFromContent = createMessagesFromExtractedContent(response.buildResult);
            setMessages(messagesFromContent);
            setHasShownInitialMessages(true);
            console.log('✅ Set', messagesFromContent.length, 'messages from unified build result (fallback)');

            // Save initial messages to database immediately
            console.log('💾 Saving initial unified build messages to database');
            saveChatHistory(response.buildResult.uuid, messagesFromContent).catch(error => {
              console.error('❌ Error saving initial unified build messages:', error);
            });
          } else {
            console.log('📝 SKIP: Not creating messages from unified build - existing build result found with chat history');
            setHasShownInitialMessages(true);
          }

          // Trigger Monaco workspace to refresh from ECS workspace
          // FIXED: Don't dispatch build complete event here - only polling should do that
          if (response.buildResult?.codeBlocks && uuid && response.workspace?.uploadSuccess && response.workspace?.filesUploaded > 0) {
            console.log('📁 Build completed with', response.buildResult.codeBlocks.length, 'code blocks');
            console.log('📁 ECS workspace upload successful:', response.workspace.filesUploaded, 'files uploaded');
            console.log('📁 Uploaded files:', response.workspace.uploadedFiles);
            console.log('⏳ Files uploaded but ECS container accessibility must be verified by polling');

            // REMOVED: Don't dispatch build complete event here - let polling verify container accessibility
            // The polling function will dispatch the event only when container is actually accessible

            console.log('📁 Files uploaded successfully, waiting for container accessibility verification');
          } else if (response.buildResult?.codeBlocks && uuid) {
            console.log('⚠️ Build completed but files not uploaded successfully to ECS workspace');
            console.log('⚠️ Upload status:', {
              codeBlocks: response.buildResult.codeBlocks.length,
              filesUploaded: response.workspace?.filesUploaded || 0,
              uploadSuccess: response.workspace?.uploadSuccess,
              ecsReady: response.workspace?.ecsReady,
              workspaceStatus: response.workspace?.status
            });
            console.log('⚠️ NOT dispatching build completion event - upload not successful');

            // This means ECS workspace wasn't ready during the build process
            // The loading page will continue to show until safety timeout
            console.log('⚠️ ECS workspace was not ready during build - starting retry mechanism');

            // FIXED: Start polling to check when ECS workspace becomes ready
            startECSWorkspacePolling(response.buildResult, uuid);
          } else if (uuid) {
            console.log('⚠️ Build response received but no code blocks found');
            console.log('⚠️ Response structure:', {
              hasBuildResult: !!response.buildResult,
              hasCodeBlocks: !!response.buildResult?.codeBlocks,
              codeBlocksLength: response.buildResult?.codeBlocks?.length || 0
            });
          }
        }

        // FIXED: Mark LLM as completed but workspace still creating until polling verifies accessibility
        console.log('✅ Frontend: LLM completed, but ECS workspace accessibility must be verified');

        setBuildProgress({
          llmGenerating: false,
          llmCompleted: true,
          workspaceCreating: true, // Still creating until polling verifies accessibility
          workspaceCompleted: false, // Not completed until container is accessible
          filesUploading: false,
          completed: false // Not completed until both LLM and workspace ready
        });

        // IMPORTANT: Keep loading state until Monaco workspace is fully initialized
        // The loading state will be cleared when Monaco workspace calls onWorkspaceReady
        console.log('⏳ Frontend: Keeping loading state until Monaco workspace is fully ready');

        // No fallback needed - backend now provides accurate upload status

        // REMOVED: No automatic final messages - system messages should only show for first build or user interactions
      } else {
        throw new Error('Failed to build with archive');
      }
    } catch (error) {
      console.error('Error building with archive:', error);

      // Stop loading state on error
      setIsLoading(false);
      setIsGeneratingCode(false);
      setBuildProgress({
        llmGenerating: false,
        llmCompleted: false,
        workspaceCreating: false,
        workspaceCompleted: false,
        filesUploading: false,
        completed: false
      });

      // Remove the thinking message and add error message
      setMessages(prev => {
        const withoutThinking = thinkingMessage
          ? prev.filter(msg => msg.id !== thinkingMessage!.id)
          : prev;
        return [...withoutThinking, {
          id: Date.now().toString(),
          type: 'ai',
          content: 'I encountered an issue while building your project. Please try refreshing the page.',
          timestamp: new Date()
        }];
      });
    }
  };




  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!prompt.trim() || isSubmitting || !uuid) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: prompt,
      timestamp: new Date()
    };

    setMessages(prev => {
      const updatedMessages = [...prev, userMessage];

      // Save user message to database
      if (currentBuildResultUuid) {
        console.log('💾 Saving user message to database');
        saveChatHistory(currentBuildResultUuid, updatedMessages).catch(error => {
          console.error('❌ Error saving user message:', error);
        });
      }

      return updatedMessages;
    });
    setPrompt('');
    setIsSubmitting(true);

    // Add "start" message immediately
    const startMessage: ChatMessage = {
      id: `start-${Date.now()}`,
      type: 'ai',
      content: '🚀 Starting to process your request...',
      timestamp: new Date()
    };
    setMessages(prev => {
      const updatedMessages = [...prev, startMessage];

      // Save start message to database
      if (currentBuildResultUuid) {
        console.log('💾 Saving start message to database');
        saveChatHistory(currentBuildResultUuid, updatedMessages).catch(error => {
          console.error('❌ Error saving start message:', error);
        });
      }

      return updatedMessages;
    });

    try {
      // Use centralized API method for chat
      const response = await buildAPI.chat({
        interviewUuid: uuid!,
        message: userMessage.content,
        conversationHistory: messages
      });

      if (!response.ok) {
        throw new Error('Failed to send message');
      }

      const data = await response.json();

      if (data.success && data.aiMessage) {
        console.log('✅ Received AI response:', data.aiMessage.content);

        // Add AI response to messages (this will replace the start message)
        setMessages(prev => {
          // Remove the start message and add the actual AI response
          const withoutStartMessage = prev.filter(msg => msg.id !== startMessage.id);
          const newMessages = [...withoutStartMessage, {
            id: data.aiMessage.id,
            type: 'ai' as const,
            content: data.aiMessage.content,
            timestamp: new Date(data.aiMessage.timestamp)
          }];

          // Save AI response to database
          if (currentBuildResultUuid) {
            console.log('💾 Saving AI response to database');
            saveChatHistory(currentBuildResultUuid, newMessages).catch(error => {
              console.error('❌ Error saving AI response:', error);
            });
          }

          console.log('✅ Chat conversation updated, total messages:', newMessages.length);
          return newMessages;
        });

        // Extract and update files in ECS workspace using backend API
        if (data.fullResponse && uuid) {
          try {
            console.log('📁 Extracting files from chat response using ECS workspace API');

            const extractData = await ecsWorkspaceAPI.extractFiles(`workspace_${uuid}`, data.fullResponse);

            if (extractData.success && extractData.data.files && Object.keys(extractData.data.files).length > 0) {
              console.log('📁 Extracted files from chat response to ECS workspace:', Object.keys(extractData.data.files));

              // Trigger Monaco workspace to refresh from ECS workspace
              window.dispatchEvent(new CustomEvent('ecs-workspace-chat-update', {
                detail: {
                  interviewUuid: uuid,
                  filesCount: Object.keys(extractData.data.files).length
                }
              }));
              console.log('✅ Dispatched ECS workspace chat update event');
            } else {
              console.log('📁 No files found in chat response');
            }
          } catch (extractError) {
            console.error('❌ Error extracting files from chat response:', extractError);
          }
        }

        // Add "complete" message after a short delay
        setTimeout(() => {
          const completeMessage: ChatMessage = {
            id: `complete-${Date.now()}`,
            type: 'ai',
            content: '✅ Request completed successfully! Your files have been updated.',
            timestamp: new Date()
          };
          setMessages(prev => {
            const newMessages = [...prev, completeMessage];

            // Save success message to database
            if (currentBuildResultUuid) {
              console.log('💾 Saving success message to database');
              saveChatHistory(currentBuildResultUuid, newMessages).catch(error => {
                console.error('❌ Error saving success message:', error);
              });
            }

            // The completion message will be saved by the debounced auto-save
            // No need for immediate save to avoid conflicts
            console.log('✅ Completion message added, will be saved by auto-save');

            return newMessages;
          });
        }, 1000);
      }
    } catch (error) {
      console.error('Error sending chat message:', error);
      // Remove start message and add error message
      setMessages(prev => {
        const withoutStartMessage = prev.filter(msg => msg.id !== startMessage.id);
        return [...withoutStartMessage, {
          id: Date.now().toString(),
          type: 'ai',
          content: 'Sorry, there was an error processing your message. Please try again.',
          timestamp: new Date()
        }];
      });
    } finally {
      setIsSubmitting(false);
    }
  };






  // File management handlers
  const handleWorkspaceReady = (workspaceId: string) => {
    console.log('🎯 BUILD PAGE: handleWorkspaceReady called (legacy)');
    console.log('✅ Monaco workspace ready:', workspaceId);
    setCurrentWorkspaceId(workspaceId);
    // Note: isWorkspaceReady is now managed by build completion events

    // Clear loading state
    setIsLoading(false);

    // Reset refresh flag after workspace is ready
    if (shouldRefreshWorkspace) {
      setShouldRefreshWorkspace(false);
    }
  };

  // Safety timeout to ensure loading state doesn't get stuck (increased to 10 minutes)
  useEffect(() => {
    if (hasCodeGenerated && !isWorkspaceReady) {
      console.log('⏳ Frontend: Code generated but workspace not ready, starting safety timeout (10 minutes)');
      const timeoutId = setTimeout(() => {
        console.log('⚠️ Frontend: Safety timeout - marking workspace as ready after 10 minutes');
        setIsWorkspaceReady(true);
        setIsLoading(false);
        // Auto-minimize will be handled by centralized logic
      }, 600000); // 10 minute timeout (much longer)

      return () => clearTimeout(timeoutId);
    }
  }, [hasCodeGenerated, isWorkspaceReady]);

  // Debug logging for state changes
  useEffect(() => {
    console.log('🔍 Frontend: State update -', {
      hasCodeGenerated,
      isWorkspaceReady,
      isLoading,
      showLoadingPage: !hasCodeGenerated || !isWorkspaceReady,
      timestamp: new Date().toISOString()
    });
  }, [hasCodeGenerated, isWorkspaceReady, isLoading]);

  // Fallback: ensure polling starts for existing builds when needed (e.g., via history drawer)
  useEffect(() => {
    if (uuid && hasCodeGenerated && !isWorkspaceReady && !isPollingRef.current) {
      console.log('🔁 FALLBACK: Starting ECS polling for existing build (via history)');
      startECSWorkspacePolling({ codeBlocks: [] }, uuid);
    }
  }, [uuid, hasCodeGenerated, isWorkspaceReady]);

  const handlePreview = async () => {
    if (!uuid) {
      alert('No project UUID available');
      return;
    }

    setIsPreviewLoading(true);

    try {
      const workspaceName = `workspace_${uuid}`;
      const data = await ecsWorkspaceAPI.startPreview(workspaceName);

      if (data.success) {
        // Notify the Monaco workspace to update preview status
        const monacoWorkspace = (window as any)[`monacoWorkspace_${uuid}`];
        if (monacoWorkspace && monacoWorkspace.setPreviewRunning) {
          monacoWorkspace.setPreviewRunning();
        }

        // Dispatch event for Monaco workspace to handle with ECS preview URL
        window.dispatchEvent(new CustomEvent('preview-started', {
          detail: { previewUrl: data.previewUrl }
        }));
      } else {
        alert(`Failed to start ECS preview server: ${data.message || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Error starting ECS preview:', error);
      alert('Failed to start ECS preview server');
    } finally {
      setIsPreviewLoading(false);
    }
  };

  const handlePublish = async () => {
    if (!uuid) {
      alert('No project UUID available');
      return;
    }
    setIsPublishing(true);
    setLiveUrl(null);
    try {
      const workspaceName = `workspace_${uuid}`;
      const data = await ecsWorkspaceAPI.startPublish(workspaceName);
      if (data.success) {
        setLiveUrl(data.liveUrl || null);
        if (data.alreadyRunning && data.liveUrl) {
          // Inform user that an existing publish was reused
          console.log('Using existing published URL:', data.liveUrl);
        }
      } else {
        alert(`Failed to publish: ${data.message || 'Unknown error'}`);
      }
    } catch (err) {
      console.error('Error publishing:', err);
      alert('Failed to publish');
    } finally {
      setIsPublishing(false);
    }
  };

  const handleRepublish = async () => {
    if (!uuid) {
      alert('No project UUID available');
      return;
    }
    setIsPublishing(true);
    setLiveUrl(null);
    try {
      const workspaceName = `workspace_${uuid}`;
      const data = await ecsWorkspaceAPI.startPublish(workspaceName, true);
      if (data.success) {
        setLiveUrl(data.liveUrl || null);
      } else {
        alert(`Failed to re-publish: ${data.message || 'Unknown error'}`);
      }
    } catch (err) {
      console.error('Error re-publishing:', err);
      alert('Failed to re-publish');
    } finally {
      setIsPublishing(false);
    }
  };

  // Auto-minimize chatbox when workspace is ready
  useEffect(() => {
    if (hasCodeGenerated && isWorkspaceReady && !isChatboxMinimized && !hasAutoMinimizedRef.current) {
      console.log('🎯 Auto-minimizing chatbox - workspace is ready');
      hasAutoMinimizedRef.current = true;
      setIsChatboxMinimized(true);
    }
  }, [hasCodeGenerated, isWorkspaceReady]);



  // Log when chatbox minimized state changes
  useEffect(() => {
    console.log('🔧 Chatbox minimized state changed:', isChatboxMinimized);
  }, [isChatboxMinimized]);

  return (
    <>
      <DashboardHeader setOpenHistoryView={setOpenHistoryView} onOpenAccount={() => setShowAccountModal(true)} />
      {showAccountModal && (
        <AccountModal setShowAccountModal={setShowAccountModal} />
      )}
      <div className="flex">
        {/* History Drawer - exact same animation and positioning as dashboard demo */}
        <AnimatePresence>
          {openHistoryView && (
            <motion.div
              initial={{ x: "-100%", width: 0, opacity: 0 }}
              animate={{ x: 0, width: "279px", opacity: 1 }}
              exit={{ x: "-100%", width: 0, opacity: 0 }}
              transition={{ duration: 0.6, ease: "easeInOut" }}
              className="absolute lg:static z-50 w-[279px] bg-drawer h-screen"
            >
              <HistoryDrawer
                setOpenHistoryView={setOpenHistoryView}
                allConversations={allConversations}
                activeConversation={activeConversation}
                setActiveConversation={handleSetActiveConversation}
                onDeleteConversation={handleDeleteConversation}
                onRenameConversation={handleRenameConversation}
              />
            </motion.div>
          )}
        </AnimatePresence>

        {/* Main content area */}
        <motion.div
          animate={{
            width: openHistoryView ? "calc(100% - 279px)" : "100%",
          }}
          transition={{
            duration: openHistoryView ? 0.6 : 0,
            ease: "easeInOut",
          }}
          className={`relative scrollbar-custom ${openHistoryView ? 'overflow-hidden' : 'overflow-auto'}`}
          style={{ height: 'calc(100vh - 146px)' }}
        >
          {/* Drawer opener button */}
          {!openHistoryView && (
            <motion.img
              src={drawerOpener}
              whileHover={{ scale: 1.1 }}
              initial={{ opacity: 0, y: 0 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 0 }}
              transition={{ duration: 0.5, ease: "easeInOut" }}
              alt="opener"
              onClick={() => setOpenHistoryView(true)}
              className="fixed lg:top-[25%] top-[30%] left-0 cursor-pointer z-50 h-[40%] md:h-[50%] lg:h-auto"
            />
          )}

          {/* Retain existing Build page content below */}
          <Box sx={{ py: 2, px: { xs: 2, md: 4 } }}>
            {/* Loading State */}
            {isLoading && (
              <Container maxWidth="lg" sx={{ pt: 4 }}>
                <Paper elevation={6} sx={{ borderRadius: 4, p: 4, textAlign: 'center' }}>
                  <CircularProgress size={60} sx={{ color: '#667eea', mb: 2 }} />
                  <Typography variant="h6" sx={{ mb: 1 }}>
                    Loading Interview Configuration...
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {uuid ? `Loading project with ID: ${uuid}` : 'Preparing your build environment...'}
                  </Typography>
                </Paper>
              </Container>
            )}

            {/* Error State */}
            {error && !isLoading && (
              <Container maxWidth="lg" sx={{ pt: 4 }}>
                <Paper elevation={6} sx={{ borderRadius: 4, p: 4 }}>
                  <Alert severity="error" sx={{ mb: 2 }}>
                    <Typography variant="h6" sx={{ mb: 1 }}>
                      Failed to Load Project
                    </Typography>
                    <Typography variant="body2">
                      {error}
                    </Typography>
                  </Alert>
                  <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>
                    <Button
                      variant="outlined"
                      startIcon={<ArrowBackIcon />}
                      onClick={() => navigate('/')}
                    >
                      Back to Home
                    </Button>
                    <Button
                      variant="contained"
                      onClick={() => window.location.reload()}
                      sx={{ bgcolor: '#667eea', '&:hover': { bgcolor: '#5a6fd8' } }}
                    >
                      Retry
                    </Button>
                  </Box>
                </Paper>
              </Container>
            )}

            {/* Main Content */}
            {!isLoading && !error && (
              <Box sx={{
                position: 'relative',
                height: 'calc(100vh - 64px)',
                display: 'flex',
                flexDirection: 'column'
              }}>
                {/* Top Navigation Bar */}
                <Container maxWidth="xl" sx={{ flexShrink: 0 }}>
                  <Box sx={{ py: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Button
                      onClick={() => {
                        if (uuid) {
                          let functionalAnswers = {};
                          let technicalAnswers = {};

                          if (interviewData?.projectData) {
                            const flatAnswers = transformNewSchemaToFlatAnswers(interviewData.projectData);
                            functionalAnswers = flatAnswers.functionalAnswers;
                            technicalAnswers = flatAnswers.technicalAnswers;
                          } else if (locationState?.functionalAnswers || locationState?.technicalAnswers) {
                            functionalAnswers = locationState.functionalAnswers || {};
                            technicalAnswers = locationState.technicalAnswers || {};
                          }

                          navigate(`/interview/${uuid}`, {
                            state: {
                              prompt: userPrompt,
                              functionalAnswers,
                              technicalAnswers
                            }
                          });
                        } else {
                          navigate('/interview');
                        }
                      }}
                      startIcon={<ArrowBackIcon />}
                      sx={{ color: '#667eea' }}
                    >
                      Edit Interview
                    </Button>

                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <button
                        className="font-bold text-[17px] leading-[100%] tracking-[0%] w-fit px-4 py-2.5 text-center !text-white bg-[#1E1E1E] rounded-[25px] cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
                        onClick={handlePublish}
                        disabled={!hasCodeGenerated || !isWorkspaceReady || isPublishing}
                      >
                        {isPublishing ? 'Publishing...' : 'Publish'}
                      </button>
                      {liveUrl && (
                        <button
                          className="font-bold text-[17px] leading-[100%] tracking-[0%] w-fit px-4 py-2.5 text-center rounded-[25px] cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed text-[#1E1E1E] border border-[#1E1E1E] bg-transparent hover:bg-[rgba(0,_0,_0,_0.06)]"
                          onClick={handleRepublish}
                          disabled={!hasCodeGenerated || !isWorkspaceReady || isPublishing}
                        >
                          {isPublishing ? 'Re-publishing...' : 'Re-publish'}
                        </button>
                      )}
                    </Box>
                  </Box>
                </Container>

                {/* Workspace Section - Full Height */}
                <Container
                  maxWidth="xl"
                  sx={{
                    flex: 1,
                    display: 'flex',
                    flexDirection: 'column',
                    mb: { xs: 3, sm: 4, md: 5 },
                    minHeight: 0
                  }}
                >
                  {(() => {
                    const shouldShowLoading = !hasCodeGenerated || !isWorkspaceReady;
                    console.log('🎯 BUILD PAGE: Render decision:', {
                      hasCodeGenerated,
                      isWorkspaceReady,
                    });

                    if (shouldShowLoading) {
                      return (
                        <LoadingPage
                          progress={buildProgress}
                          notice={buildProgress.workspaceCreating ? 'Workspace was not found. Creating a new one...' : undefined}
                        />
                      );
                    }

                    return (
                      <>
                        {liveUrl && (
                          <Alert severity="success" sx={{ mb: 2 }}>
                            <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                              Published URL
                            </Typography>
                            <a href={liveUrl} target="_blank" rel="noreferrer">{liveUrl}</a>
                          </Alert>
                        )}
                        <MonacoWorkspace
                          interviewUuid={uuid}
                          onWorkspaceReady={handleWorkspaceReady}
                          onError={(error: string) => {
                            console.error('Workspace error:', error);
                            setIsWorkspaceReady(true);
                            setIsLoading(false);
                            // Auto-minimize will be handled by centralized logic
                          }}
                          height={isChatboxMinimized ? "calc(100vh - 100px)" : "calc(100vh - 200px)"}
                        />
                      </>
                    );
                  })()}
                </Container>

                {/* Fixed Floating Chatbox at Bottom */}
                <Box
                  sx={{
                    position: 'fixed',
                    bottom: 0,
                    left: '50%',
                    transform: 'translateX(-50%)',
                    width: { xs: '95%', sm: '75%', md: '55%' },
                    maxWidth: '900px',
                    zIndex: 1000,
                    boxShadow: 'none',
                    borderRadius: 0,
                    transition: 'all 0.3s ease-in-out'
                  }}
                >
                  <BuildChatbox
                    prompt={prompt}
                    setPrompt={setPrompt}
                    handleSubmit={handleSubmit}
                    messages={messages}
                    isSubmitting={isSubmitting}
                    hasCodeGenerated={hasCodeGenerated}
                    isLLMGenerating={buildProgress.llmGenerating}
                    onMinimizeChange={setIsChatboxMinimized}
                    isMinimized={isChatboxMinimized}
                  />
                </Box>
              </Box>
            )}
          </Box>
        </motion.div>
      </div>
    </>
  );
};

export default Build;