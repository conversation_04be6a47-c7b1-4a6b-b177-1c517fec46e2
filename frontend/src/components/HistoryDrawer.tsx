import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Popover, Modal } from 'antd';
import flowerIcon from '../assets/images/flowerIcon.svg';
import flowerOpenedIcon from '../assets/images/flowerOpenedIcon.svg';
import arrowIcon from '../assets/images/arrowIcon.svg';

export interface ConversationItem {
  _id: string;
  title: string;
  createdAt: string;
  updatedAt: string;
}

interface HistoryDrawerProps {
  setOpenHistoryView: (value: boolean) => void;
  allConversations: ConversationItem[];
  activeConversation: string;
  setActiveConversation: (value: string) => void;
  onDeleteConversation: (id: string) => void;
  onRenameConversation: (id: string, newTitle: string) => void;
}

const HistoryDrawer: React.FC<HistoryDrawerProps> = ({
  setOpenHistoryView,
  allConversations,
  activeConversation,
  setActiveConversation,
  onDeleteConversation,
  onRenameConversation
}) => {
  const [openMenu, setOpenMenu] = useState<number | null | boolean>(null);
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [renameValue, setRenameValue] = useState("");
  const [updateConversation, setUpdateConversation] = useState<ConversationItem | null>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [conversationToDelete, setConversationToDelete] = useState<ConversationItem | null>(null);

  const filteredConversations = allConversations.filter(
    (item: ConversationItem) =>
      (item.title || '').toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleRenameConversation = () => {
    setUpdateConversation(null);
    if (updateConversation && renameValue.trim()) {
      onRenameConversation(updateConversation._id, renameValue);
    }
    setRenameValue("");
  };

  const handleDeleteConversation = () => {
    if (!conversationToDelete) return;
    
    onDeleteConversation(conversationToDelete._id);
    setConversationToDelete(null);
    setIsDeleteModalOpen(false);
    setOpenMenu(false);
  };

  const openDeleteModal = (conversation: ConversationItem) => {
    setConversationToDelete(conversation);
    setIsDeleteModalOpen(true);
    setOpenMenu(false);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    let day: string | number = date.getUTCDate();
    day = day < 10 ? `0${day}` : `${day}`;
    return day;
  };

  const formatMonth = (dateString: string) => {
    const date = new Date(dateString);
    const monthName = date.toLocaleString("en-US", { month: "long" });
    return monthName;
  };

  return (
    <>
      <div 
        className="relative bg-drawer"
        onWheel={(e) => e.stopPropagation()}
      >
        <div
          className="bg-[linear-gradient(168.12deg,_rgba(255,_255,_255,_0.4)_0%,_rgba(255,_255,_255,_0.1)_98.85%)] backdrop-blur-[40px] shadow-[0px_4px_24px_-1px_rgba(136,_136,_136,_0.12)] cursor-pointer w-fit p-1 absolute -right-3 top-4 rounded-[8px] z-50"
          style={{ backdropFilter: "blur(40px)" }}
          onClick={() => setOpenHistoryView(false)}
        >
          <img src={flowerOpenedIcon} alt="icon" />
        </div>

        <div>
          <div
            className="border-[0.3px] backdrop-blur-[40px] bg-[linear-gradient(168.12deg,rgba(255,255,255,0.4)_0%,rgba(255,255,255,0.1)_98.85%)] shadow-[0px_4px_24px_-1px_#8888881F] py-3.5 px-4 my-3.5 ms-1.5 me-6 rounded-lg bg-white/30"
            style={{
              borderImageSource:
                "linear-gradient(170.39deg, rgba(254, 218, 204, 0.75) 1.06%, rgba(254, 188, 154, 0.75) 98.07%)",
              borderImageSlice: 1,
            }}
          >
            <h1 className="text-[#222222] font-bold text-[20px] leading-[133%] tracking-[0%]">
              History
            </h1>
          </div>

          <input
            type="text"
            className="shadow-[0px_1px_2px_0px_rgba(55,55,55,0.1)] bg-[#FFFFFF80] w-full px-2 hover:border-0 mb-4"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        <div 
          className="overflow-y-auto historyList scrollbar-hide overflow-x-hidden"
          onWheel={(e) => e.stopPropagation()}
        >
          {filteredConversations.map((item: ConversationItem, index) => {
            return (
              <motion.div
                key={index}
                whileHover={{
                  scaleX: 1.02,
                  originX: 0,
                }}
                animate={{
                  backgroundColor:
                    item?._id === activeConversation ? "#ababab" : "#ffffff",
                }}
                onClick={() => {
                  setActiveConversation(item?._id);
                  setOpenMenu(false);
                }}
                className="relative flex justify-between border-[0.2px] border-[#444444] bg-white items-center py-5 gap-4.5 pe-5 ps-9 cursor-pointer w-[274px]"
              >
                <span className="absolute top-[39px] -left-10 border-[0.2px] border-[#444444] -rotate-90 bg-[#ECECEC] py-0.5 text-[18px] mx-auto w-[40.5%] text-center">
                  {formatMonth(item?.updatedAt).toUpperCase()}
                </span>

                <span className="font-normal text-[70px] leading-[100%] tracking-[-0.04em] w-full text-center">
                  {formatDate(item?.updatedAt)}
                </span>

                <span className="font-light text-[20px] leading-[100%] tracking-[-0.04em] w-full truncate-title">
                  {item.title}
                </span>

                <div className="relative">
                  <Popover
                    placement="right"
                    content={
                      <>
                        <AnimatePresence>
                          {openMenu === index && (
                            <>
                              <motion.div
                                initial={{ opacity: 0, y: 0 }}
                                animate={{ opacity: 1, y: 0 }}
                                exit={{ opacity: 0, y: 0 }}
                                transition={{
                                  duration: 0.5,
                                  ease: "easeInOut",
                                }}
                                className="items-start left-16 -bottom-6 z-50 flex flex-col gap-2 w-max font-['Inter']"
                                onClick={(e) => e.stopPropagation()}
                              >
                                <button
                                  className="font-bold text-[17px] leading-[100%] tracking-[0%] w-fit px-4 py-2.5 text-center !text-white bg-[#1E1E1E] rounded-[25px] cursor-pointer"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setUpdateConversation(item);
                                    setRenameValue(item.title);
                                    setOpenMenu(false);
                                  }}
                                >
                                  Rename
                                </button>
                                <button
                                  className="font-bold text-[17px] leading-[100%] tracking-[0%] w-fit px-4 py-2.5 text-center !text-white bg-[#1E1E1E] rounded-[25px] cursor-pointer"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    openDeleteModal(item);
                                  }}
                                >
                                  Delete
                                </button>
                              </motion.div>
                            </>
                          )}
                        </AnimatePresence>
                      </>
                    }
                    trigger="click"
                    open={openMenu === index}
                    onOpenChange={() => setOpenMenu(!openMenu)}
                  >
                    <motion.img
                      src={flowerIcon}
                      alt="icon"
                      className="cursor-pointer"
                      whileHover={{
                        scale: 1.3,
                      }}
                      animate={{
                        rotate: openMenu === index ? 225 : 0,
                        filter:
                          openMenu === index
                            ? "brightness(0) saturate(100%)"
                            : "brightness(1) saturate(100%)",
                      }}
                      transition={{ duration: 0.5, ease: "easeInOut" }}
                      onClick={(event) => {
                        event.stopPropagation();
                        if (openMenu === index) {
                          setOpenMenu(null);
                        } else {
                          setOpenMenu(index);
                        }
                      }}
                    />
                  </Popover>
                </div>
              </motion.div>
            );
          })}
          {filteredConversations.length < 1 && (
            <>
              <p className="text-center">No Result Found</p>
            </>
          )}
        </div>
      </div>

      <Modal
        title={`Update Conversation - ${
          updateConversation ? updateConversation.title : ""
        }`}
        open={!!updateConversation}
        footer={null}
        onCancel={() => setUpdateConversation(null)}
        styles={{
          body: { maxHeight: "70vh", overflowY: "auto" },
        }}
      >
        <div className="m-5" onClick={(e) => e.stopPropagation()}>
          <div className="relative">
            <input
              type="text"
              className="w-[95%] bg-[#F6F6F6] py-4 rounded-[25px] ps-7 pe-14 outline-none"
              onChange={(e) => setRenameValue(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && handleRenameConversation()}
              placeholder="conversation"
              value={renameValue}
            />
            <button
              className="bg-[#1E1E1E] py-4.5 px-6 rounded-[25px] absolute top-0 -right-2 cursor-pointer disabled:cursor-not-allowed disabled:opacity-50"
              onClick={handleRenameConversation}
              disabled={!renameValue.length}
            >
              <img src={arrowIcon} alt="icon" height={12} width={14} />
            </button>
          </div>
        </div>
      </Modal>

      {/* Delete Confirmation Modal - Same as RecentProjects */}
      <Modal
        title="Delete Project"
        open={isDeleteModalOpen}
        onCancel={() => {
          setIsDeleteModalOpen(false);
          setConversationToDelete(null);
        }}
        onOk={handleDeleteConversation}
        okText="Delete"
        cancelText="Cancel"
        okButtonProps={{ danger: true }}
      >
        <p>Are you sure you want to delete "{conversationToDelete?.title}"?</p>
        <p className="text-gray-500 text-sm mt-2">This action cannot be undone.</p>
      </Modal>
    </>
  );
};

export default HistoryDrawer; 